# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Running the Application
```bash
# Run analysis without installation
python main.py analyze RELIANCE.BO

# With options
python main.py analyze RELIANCE.BO --period 1y --risk moderate --format table --enhanced

# Install as package (optional)
pip install -e .
stock-analyzer analyze RELIANCE.BO
```

### Testing
```bash
# Run all tests
python -m pytest

# Run specific test suites
python -m pytest tests/unit/ -v
python -m pytest tests/cli/ -v
python -m pytest tests/integration/ -v

# Run specific test files
python -m pytest tests/unit/test_indicators.py -v
python -m pytest tests/unit/test_formatters.py -v

# Run with coverage
python -m pytest --cov=src tests/

# Run tests with specific markers
python -m pytest -m "not slow"
python -m pytest -m integration
```

### Dependencies
```bash
# Install dependencies
pip install -r requirements.txt

# Install development dependencies (includes pytest, pytest-cov)
pip install -e .
```

## Architecture Overview

### Core Components

**CLI Layer** (`src/cli/`)
- `commands.py`: Click-based CLI interface with analyze command
- `formatters.py`: Output formatting (table, JSON, CSV) with error handling

**Data Layer** (`src/data/`)
- `yahoo_client.py`: Yahoo Finance API client for BSE stock data retrieval

**Analysis Layer** (`src/analysis/`)
- `indicators.py`: Technical indicators (RSI, SMA, Bollinger Bands, MACD, etc.)
- `fundamental.py`: Fundamental analysis calculations

**Engine Layer** (`src/engine/`)
- `decision_engine.py`: Main orchestration engine for stock analysis
- `enhanced_recommendation_engine.py`: Multi-factor recommendation system with weighted scoring
- `technical_scorer.py`: Technical analysis scoring component
- `fundamental_scorer.py`: Fundamental analysis scoring component
- `risk_scorer.py`: Risk assessment scoring
- `market_context_analyzer.py`: Market context analysis

### Data Flow
1. CLI receives user input (stock symbol, parameters)
2. YahooClient fetches stock data from Yahoo Finance
3. DecisionEngine orchestrates analysis through multiple scorers
4. Technical/Fundamental/Risk analysis components process data
5. EnhancedRecommendationEngine aggregates scores with weighted factors
6. Results formatted and output via CLI formatters

### Key Design Patterns
- **Strategy Pattern**: Multiple formatters (table, JSON, CSV) implementing common interface
- **Engine Pattern**: DecisionEngine orchestrates multiple analysis components
- **Scorer Pattern**: Separate scorers for technical, fundamental, risk, and market context analysis
- **Weighted Scoring**: EnhancedRecommendationEngine combines multiple factors with configurable weights

### BSE Stock Symbols
- All symbols use `.BO` suffix (e.g., `RELIANCE.BO`, `TCS.BO`, `INFY.BO`)
- Symbol validation is case-insensitive but normalized to uppercase

### Risk Tolerance Levels
- `conservative`: Lower risk appetite, more stringent thresholds
- `moderate`: Balanced approach (default)
- `aggressive`: Higher risk tolerance for potentially higher returns

### Time Periods
- Supported periods: `1m`, `3m`, `6m`, `1y`, `2y`
- Default analysis period is `1y`