import json
from datetime import datetime
from typing import Dict, Any, List
from abc import ABC, abstractmethod

def format_currency(amount: float) -> str:
    """Format amount as Indian Rupee currency"""
    if amount is None:
        return "N/A"
    
    try:
        amount = float(amount)
    except (ValueError, TypeError):
        return "N/A"
    
    def indian_format(num):
        """Format number with Indian numbering system"""
        if num == 0:
            return "0.00"
        
        # Convert to string and split at decimal
        num_str = f"{num:.2f}"
        integer_part, decimal_part = num_str.split('.')
        
        # Add commas in Indian style (last 3 digits, then every 2)
        if len(integer_part) <= 3:
            return f"{integer_part}.{decimal_part}"
        
        # Reverse for easier processing
        reversed_digits = integer_part[::-1]
        formatted = ""
        
        # First group of 3 digits
        formatted += reversed_digits[:3]
        remaining = reversed_digits[3:]
        
        # Then groups of 2
        i = 0
        while i < len(remaining):
            if formatted:
                formatted += ","
            formatted += remaining[i:i+2]
            i += 2
        
        # Reverse back
        formatted = formatted[::-1]
        return f"{formatted}.{decimal_part}"
    
    if amount < 0:
        return f"-₹{indian_format(abs(amount))}"
    return f"₹{indian_format(amount)}"

def format_percentage(value: float, precision: int = 1) -> str:
    """Format decimal as percentage"""
    if value is None:
        return "N/A"
    
    try:
        value = float(value)
    except (ValueError, TypeError):
        return "N/A"
    
    return f"{value * 100:.{precision}f}%"

class BaseFormatter(ABC):
    """Base class for output formatters"""
    
    @abstractmethod
    def format_analysis(self, result: Dict[str, Any], **kwargs) -> str:
        """Format analysis result"""
        pass
    
    @abstractmethod
    def format_error(self, error_message: str) -> str:
        """Format error message"""
        pass

class TerminalFormatter(BaseFormatter):
    """Format output for terminal display"""
    
    def _format_box_line(self, text: str) -> str:
        """Helper to format a line within a box with proper padding"""
        return f"│ {text}" + " " * (48 - len(text)) + "│"
    
    def format_analysis(self, result: Dict[str, Any], detailed: bool = False) -> str:
        """Format analysis result for terminal"""
        symbol = result.get('symbol', 'N/A')
        
        # Handle recommendation field - could be string or dict
        recommendation_raw = result.get('recommendation', 'HOLD')
        if isinstance(recommendation_raw, dict):
            recommendation = recommendation_raw.get('action', 'HOLD')
            # If confidence not in main result, try to get from recommendation dict
            if 'confidence' not in result and 'confidence' in recommendation_raw:
                confidence = recommendation_raw.get('confidence', 0.0)
            else:
                confidence = result.get('confidence', 0.0)
        else:
            recommendation = recommendation_raw
            confidence = result.get('confidence', 0.0)
            
        current_price = result.get('current_price', 0.0)
        
        # Basic formatting with required elements
        output = []
        output.append(f"BSE Stock Analysis: {symbol}")
        output.append("═" * 50)
        output.append("")
        
        # Price line
        price_str = format_currency(current_price) if current_price else "N/A"
        output.append(f"Current Price: {price_str}")
        output.append("")
        
        # Recommendation box  
        confidence_pct = format_percentage(confidence, precision=0) if confidence else "N/A"
        output.append("┌─ RECOMMENDATION " + "─" * 32 + "┐")
        output.append(f"│ {recommendation} (Confidence: {confidence_pct})" + " " * (48 - len(f"{recommendation} (Confidence: {confidence_pct})")) + "│")
        output.append("└" + "─" * 49 + "┘")
        output.append("")
        
        # Show all key metrics that led to the recommendation
        output.extend(self._format_key_metrics_detailed(result, detailed))
        
        return "\n".join(output)
    
    def _format_key_metrics_detailed(self, result: Dict[str, Any], detailed: bool = False) -> List[str]:
        """Format all key metrics that led to the recommendation"""
        output = []
        
        # Technical Analysis Metrics
        output.extend(self._format_technical_metrics(result, detailed))
        
        # Volume Analysis
        output.extend(self._format_volume_analysis(result, detailed))
        
        # Support and Resistance (detailed mode only)
        if detailed:
            output.extend(self._format_support_resistance(result))
        
        # Risk Assessment (detailed mode only)
        if detailed:
            output.extend(self._format_risk_metrics(result))
        
        # Enhanced Factor Scores
        output.extend(self._format_enhanced_factors(result))
        
        # Decision Factors
        output.extend(self._format_decision_factors(result))
        
        return output
    
    def _get_rsi_status(self, rsi: float) -> str:
        """Get RSI status description"""
        try:
            rsi = float(rsi)
            if rsi > 70:
                return "Overbought"
            elif rsi < 30:
                return "Oversold"
            else:
                return "Neutral"
        except (ValueError, TypeError):
            return "Invalid"
    
    def _format_technical_metrics(self, result: Dict[str, Any], detailed: bool = False) -> List[str]:
        """Format technical analysis metrics section"""
        output = []
        output.append("┌─ KEY METRICS " + "─" * 35 + "┐")
        
        # Trend indicators
        trend_signal = result.get('trend_signal', 'neutral')
        momentum_signal = result.get('momentum_signal', 'neutral')
        output.append(self._format_box_line(f"Trend Signal: {trend_signal.upper()}"))
        output.append(self._format_box_line(f"Momentum Signal: {momentum_signal.upper()}"))
        
        # Moving averages
        for period in [20, 50, 200]:
            sma_value = result.get(f'sma_{period}')
            if sma_value:
                output.append(self._format_box_line(f"SMA {period}: {format_currency(sma_value)}"))
        
        # RSI
        rsi = result.get('rsi')
        if rsi:
            try:
                rsi_float = float(rsi)
                rsi_status = self._get_rsi_status(rsi)
                output.append(self._format_box_line(f"RSI (14): {rsi_float:.1f} ({rsi_status})"))
            except (ValueError, TypeError):
                # Skip malformed RSI data
                pass
        
        # MACD
        output.extend(self._format_macd_data(result))
        
        # Bollinger Bands
        output.extend(self._format_bollinger_bands(result, detailed))
        
        output.append("└" + "─" * 49 + "┘")
        output.append("")
        return output
    
    def _get_macd_crossover_status(self, macd_crossover: int) -> str:
        """Get MACD crossover status description"""
        if macd_crossover == 1:
            return "Bullish Cross"
        elif macd_crossover == -1:
            return "Bearish Cross"
        else:
            return "No Cross"

    def _format_macd_data(self, result: Dict[str, Any]) -> List[str]:
        """Format MACD indicator data"""
        output = []
        macd_line = result.get('macd_line')
        macd_signal = result.get('macd_signal')
        macd_crossover = result.get('macd_crossover', 0)
        
        if macd_line is not None and macd_signal is not None:
            output.append(self._format_box_line(f"MACD: {macd_line:.3f} / {macd_signal:.3f}"))
            
            crossover_text = self._get_macd_crossover_status(macd_crossover)
            output.append(self._format_box_line(f"MACD Signal: {crossover_text}"))
        
        return output
    
    def _format_bollinger_bands(self, result: Dict[str, Any], detailed: bool = False) -> List[str]:
        """Format Bollinger Bands data"""
        output = []
        bb_status = result.get('bb_status', 'unknown')
        bollinger_percent_b = result.get('bollinger_percent_b')
        bb_upper = result.get('bb_upper')
        bb_lower = result.get('bb_lower')
        
        has_bb_data = bb_status == 'available' or bb_upper or bb_lower
        
        if has_bb_data:
            if bollinger_percent_b is not None:
                bb_position = self._get_bb_position(bollinger_percent_b)
                output.append(self._format_box_line(f"%B Position: {bollinger_percent_b:.2f} ({bb_position})"))
            
            if bb_upper and (bb_lower or detailed):
                output.append(self._format_box_line(f"Bollinger Upper: {format_currency(bb_upper)}"))
            if bb_lower:
                output.append(self._format_box_line(f"Bollinger Lower: {format_currency(bb_lower)}"))
            
            # Squeeze and breakout info
            output.extend(self._format_bb_squeeze_breakout(result))
            
        elif bb_status and 'insufficient_data' in bb_status:
            output.append(self._format_box_line(f"BB Analysis: {bb_status}"))
        
        return output
    
    def _get_bb_position(self, bollinger_percent_b: float) -> str:
        """Get Bollinger Band position description"""
        if bollinger_percent_b > 0.8:
            return "Overbought"
        elif bollinger_percent_b < 0.2:
            return "Oversold"
        else:
            return "Middle Range"
    
    def _format_bb_squeeze_breakout(self, result: Dict[str, Any]) -> List[str]:
        """Format Bollinger Band squeeze and breakout information"""
        output = []
        bollinger_squeeze = result.get('bollinger_squeeze', {})
        bollinger_breakout = result.get('bollinger_breakout', {})
        
        if bollinger_squeeze.get('is_squeeze', False):
            intensity = bollinger_squeeze.get('squeeze_intensity', 0)
            output.append(self._format_box_line(f"BB Squeeze: Active ({intensity:.1f})"))
        
        if bollinger_breakout.get('upper_breakout') or bollinger_breakout.get('lower_breakout'):
            breakout_type = "Upper" if bollinger_breakout.get('upper_breakout') else "Lower"
            strength = bollinger_breakout.get('breakout_strength', 0)
            output.append(self._format_box_line(f"BB Breakout: {breakout_type} ({strength:.2f})"))
        
        return output
    
    def _format_volume_analysis(self, result: Dict[str, Any], detailed: bool) -> List[str]:
        """Format volume analysis section"""
        output = []
        relative_volume = result.get('relative_volume', 1.0)
        current_volume = result.get('current_volume', 0)
        volume_sma = result.get('volume_sma')
        
        show_volume_analysis = detailed or (relative_volume and (relative_volume > 1.2 or relative_volume < 0.8))
        
        if show_volume_analysis:
            output.append("┌─ VOLUME ANALYSIS " + "─" * 31 + "┐")
            
            if relative_volume:
                vol_status = self._get_volume_status(relative_volume)
                output.append(self._format_box_line(f"Relative Volume: {relative_volume:.2f} ({vol_status})"))
            
            if detailed:
                if current_volume:
                    output.append(self._format_box_line(f"Current Volume: {current_volume:,}"))
                if volume_sma:
                    output.append(self._format_box_line(f"Volume SMA: {volume_sma:,.0f}"))
            
            output.append("└" + "─" * 49 + "┘")
            output.append("")
        
        return output
    
    def _get_volume_status(self, relative_volume: float) -> str:
        """Get volume status description"""
        if relative_volume > 1.5:
            return "High"
        elif relative_volume > 1.2:
            return "Above Avg"
        elif relative_volume < 0.8:
            return "Below Avg"
        else:
            return "Normal"
    
    def _format_support_resistance(self, result: Dict[str, Any]) -> List[str]:
        """Format support and resistance levels section"""
        output = []
        support_levels = result.get('support_levels', [])
        resistance_levels = result.get('resistance_levels', [])
        current_price = result.get('current_price', 0)
        
        if support_levels or resistance_levels:
            output.append("┌─ SUPPORT & RESISTANCE " + "─" * 25 + "┐")
            
            if support_levels and current_price:
                nearby_support = [s for s in support_levels if s < current_price][:3]
                for i, level in enumerate(nearby_support):
                    output.append(self._format_box_line(f"Support {i+1}: {format_currency(level)}"))
            
            if resistance_levels and current_price:
                nearby_resistance = [r for r in resistance_levels if r > current_price][:3]
                for i, level in enumerate(nearby_resistance):
                    output.append(self._format_box_line(f"Resistance {i+1}: {format_currency(level)}"))
            
            output.append("└" + "─" * 49 + "┘")
            output.append("")
        
        return output
    
    def _format_risk_metrics(self, result: Dict[str, Any]) -> List[str]:
        """Format risk assessment section"""
        output = []
        risk_assessment = result.get('risk_assessment')
        
        if risk_assessment is not None:
            output.append("┌─ RISK METRICS " + "─" * 34 + "┐")
            
            volatility = risk_assessment.get('volatility')
            if volatility:
                vol_level = self._get_volatility_level(volatility)
                output.append(self._format_box_line(f"Volatility: {format_percentage(volatility)} ({vol_level})"))
            
            max_drawdown = risk_assessment.get('max_drawdown')
            if max_drawdown is not None:
                output.append(self._format_box_line(f"Max Drawdown: {format_percentage(abs(max_drawdown))}"))
            
            sharpe_ratio = risk_assessment.get('sharpe_ratio')
            if sharpe_ratio is not None:
                sharpe_quality = self._get_sharpe_quality(sharpe_ratio)
                output.append(self._format_box_line(f"Sharpe Ratio: {sharpe_ratio:.2f} ({sharpe_quality})"))
            
            risk_level = risk_assessment.get('risk_level', 'MEDIUM')
            output.append(self._format_box_line(f"Risk Level: {risk_level}"))
            
            output.append("└" + "─" * 49 + "┘")
            output.append("")
        
        return output
    
    def _get_volatility_level(self, volatility: float) -> str:
        """Get volatility level description"""
        if volatility > 0.4:
            return "Very High"
        elif volatility > 0.25:
            return "High"
        elif volatility > 0.15:
            return "Medium"
        else:
            return "Low"
    
    def _get_sharpe_quality(self, sharpe_ratio: float) -> str:
        """Get Sharpe ratio quality description"""
        if sharpe_ratio > 2:
            return "Excellent"
        elif sharpe_ratio > 1:
            return "Good"
        elif sharpe_ratio > 0:
            return "Fair"
        else:
            return "Poor"
    
    def _format_enhanced_factors(self, result: Dict[str, Any]) -> List[str]:
        """Format enhanced factor analysis section"""
        output = []
        factor_scores = result.get('factor_scores', {})
        enhanced_rec = result.get('enhanced_recommendation', {})
        
        if factor_scores or enhanced_rec:
            output.append("┌─ ENHANCED FACTOR ANALYSIS " + "─" * 21 + "┐")
            
            if factor_scores:
                output.extend(self._format_factor_scores(factor_scores))
            
            if enhanced_rec:
                output.extend(self._format_risks_opportunities(enhanced_rec))
            
            output.append("└" + "─" * 49 + "┘")
            output.append("")
        
        return output
    
    def _format_factor_scores(self, factor_scores: Dict[str, Any]) -> List[str]:
        """Format individual factor scores"""
        output = []
        factors = [
            ('technical', 'Technical Score'),
            ('fundamental', 'Fundamental Score'),
            ('risk', 'Risk Score'),
            ('market_context', 'Market Context')
        ]
        
        for factor_key, factor_label in factors:
            factor_data = factor_scores.get(factor_key, {})
            if factor_data.get('score') is not None:
                score = factor_data['score']
                output.append(self._format_box_line(f"{factor_label}: {score:+.2f}"))
        
        composite_score = factor_scores.get('composite_score')
        if composite_score is not None:
            output.append(self._format_box_line(f"Composite Score: {composite_score:+.2f}"))
        
        return output
    
    def _format_risks_opportunities(self, enhanced_rec: Dict[str, Any]) -> List[str]:
        """Format key risks and opportunities"""
        output = []
        key_risks = enhanced_rec.get('key_risks', [])
        key_opportunities = enhanced_rec.get('key_opportunities', [])
        
        if key_risks:
            risks_text = self._truncate_text(", ".join(key_risks[:2]), 40)
            output.append(self._format_box_line(f"Key Risks: {risks_text}"))
        
        if key_opportunities:
            opps_text = self._truncate_text(", ".join(key_opportunities[:2]), 40)
            output.append(self._format_box_line(f"Opportunities: {opps_text}"))
        
        return output
    
    def _truncate_text(self, text: str, max_length: int) -> str:
        """Truncate text to fit within specified length"""
        if len(text) > max_length:
            return text[:max_length-3] + "..."
        return text
    
    def _format_decision_factors(self, result: Dict[str, Any]) -> List[str]:
        """Format decision factors section"""
        output = []
        output.append("┌─ DECISION FACTORS " + "─" * 30 + "┐")
        
        # Extract recommendation details
        recommendation_data = self._extract_recommendation_data(result)
        
        # Display key factors
        output.append(self._format_box_line(f"Final Action: {recommendation_data['action']}"))
        output.append(self._format_box_line(f"Confidence: {format_percentage(recommendation_data['confidence'])}"))
        
        if recommendation_data['target_price']:
            output.append(self._format_box_line(f"Target Price: {format_currency(recommendation_data['target_price'])}"))
        if recommendation_data['stop_loss']:
            output.append(self._format_box_line(f"Stop Loss: {format_currency(recommendation_data['stop_loss'])}"))
        
        # Add trading signals
        output.extend(self._format_trading_signals(result))
        
        output.append("└" + "─" * 49 + "┘")
        return output
    
    def _extract_recommendation_data(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and normalize recommendation data"""
        confidence = result.get('confidence', 0.0)
        recommendation_raw = result.get('recommendation', 'HOLD')
        
        if isinstance(recommendation_raw, dict):
            return {
                'action': recommendation_raw.get('action', 'HOLD'),
                'confidence': confidence,
                'target_price': result.get('target_price') or recommendation_raw.get('target_price'),
                'stop_loss': result.get('stop_loss') or recommendation_raw.get('stop_loss')
            }
        else:
            return {
                'action': recommendation_raw,
                'confidence': confidence,
                'target_price': result.get('target_price'),
                'stop_loss': result.get('stop_loss')
            }
    
    def _format_trading_signals(self, result: Dict[str, Any]) -> List[str]:
        """Format bullish and bearish trading signals"""
        output = []
        bullish_signals = []
        bearish_signals = []
        
        # Collect signals
        trend_signal = result.get('trend_signal', 'neutral')
        momentum_signal = result.get('momentum_signal', 'neutral')
        rsi = result.get('rsi')
        relative_volume = result.get('relative_volume', 1.0)
        
        if trend_signal == 'bullish':
            bullish_signals.append('Positive trend')
        elif trend_signal == 'bearish':
            bearish_signals.append('Negative trend')
        
        if momentum_signal == 'bullish':
            bullish_signals.append('Strong momentum')
        elif momentum_signal == 'bearish':
            bearish_signals.append('Weak momentum')
        
        if rsi:
            try:
                rsi_float = float(rsi)
                if rsi_float < 30:
                    bullish_signals.append('Oversold RSI')
                elif rsi_float > 70:
                    bearish_signals.append('Overbought RSI')
            except (ValueError, TypeError):
                # Skip malformed RSI data
                pass
        
        if relative_volume and relative_volume > 1.2:
            bullish_signals.append('High volume')
        
        # Format signals
        if bullish_signals:
            signals_text = self._truncate_text(", ".join(bullish_signals[:2]), 40)
            output.append(self._format_box_line(f"Bullish Signals: {signals_text}"))
        
        if bearish_signals:
            signals_text = self._truncate_text(", ".join(bearish_signals[:2]), 40)
            output.append(self._format_box_line(f"Bearish Signals: {signals_text}"))
        
        return output
    
    def format_error(self, error_message: str) -> str:
        """Format error message for terminal"""
        return f"Error: {error_message}"

class JSONFormatter(BaseFormatter):
    """Format output as JSON"""
    
    def format_analysis(self, result: Dict[str, Any], pretty: bool = False) -> str:
        """Format analysis result as JSON"""
        # Create structured JSON output
        json_output = {
            'symbol': result.get('symbol', 'N/A'),
            'timestamp': datetime.now().isoformat() + 'Z',
            'current_price': result.get('current_price', 0.0),
            'recommendation': {
                'action': result.get('recommendation', 'HOLD'),
                'confidence': result.get('confidence', 0.0),
                'target_price': result.get('target_price', 0.0),
                'stop_loss': result.get('stop_loss', 0.0),
                'risk_level': result.get('risk_level', 'MEDIUM')
            },
            'technical_indicators': {
                'sma_20': result.get('sma_20'),
                'sma_50': result.get('sma_50'),
                'sma_200': result.get('sma_200'),
                'rsi': result.get('rsi'),
                'macd': {
                    'line': result.get('macd_line'),
                    'signal': result.get('macd_signal'),
                    'histogram': result.get('macd_histogram'),
                    'crossover': result.get('macd_crossover')
                },
                'bollinger_bands': {
                    'upper': result.get('bb_upper'),
                    'middle': result.get('bb_middle'),
                    'lower': result.get('bb_lower')
                }
            },
            'volume_analysis': {
                'current_volume': result.get('current_volume', 0),
                'volume_sma': result.get('volume_sma'),
                'relative_volume': result.get('relative_volume')
            },
            'signals': {
                'trend_signal': result.get('trend_signal', 'neutral'),
                'momentum_signal': result.get('momentum_signal', 'neutral')
            }
        }
        
        if pretty:
            return json.dumps(json_output, indent=2, default=str)
        return json.dumps(json_output, default=str)
    
    def format_error(self, error_message: str) -> str:
        """Format error message as JSON"""
        error_output = {
            'error': error_message,
            'timestamp': datetime.now().isoformat() + 'Z'
        }
        return json.dumps(error_output)

class CSVFormatter(BaseFormatter):
    """Format output as CSV"""
    
    def format_analysis(self, result: Dict[str, Any]) -> str:
        """Format analysis result as CSV"""
        # Define CSV headers
        headers = [
            'symbol', 'current_price', 'recommendation', 'confidence',
            'sma_20', 'sma_50', 'rsi', 'target_price', 'stop_loss'
        ]
        
        # Create data row
        data = [
            result.get('symbol', 'N/A'),
            result.get('current_price', 0.0),
            result.get('recommendation', 'HOLD'),
            result.get('confidence', 0.0),
            result.get('sma_20', ''),
            result.get('sma_50', ''),
            result.get('rsi', ''),
            result.get('target_price', ''),
            result.get('stop_loss', '')
        ]
        
        # Convert to strings and handle special characters
        data_str = []
        for item in data:
            if item is None or item == '':
                data_str.append('')
            elif isinstance(item, str) and (',' in item or '"' in item):
                # Escape quotes and wrap in quotes
                escaped = item.replace('"', '""')
                data_str.append(f'"{escaped}"')
            else:
                data_str.append(str(item))
        
        # Return CSV format
        header_line = ','.join(headers)
        data_line = ','.join(data_str)
        return f"{header_line}\n{data_line}"
    
    def format_batch(self, results: List[Dict[str, Any]]) -> str:
        """Format batch results as CSV"""
        if not results:
            return "symbol,current_price,recommendation"
        
        # Use the first result to get headers, then format all results
        lines = []
        for i, result in enumerate(results):
            formatted = self.format_analysis(result)
            if i == 0:
                lines.append(formatted)  # Include header for first result
            else:
                lines.append(formatted.split('\n')[1])  # Only data line for subsequent results
        
        return '\n'.join(lines)
    
    def format_error(self, error_message: str) -> str:
        """Format error message as CSV"""
        return f"error\n{error_message}"

def get_formatter(format_type: str) -> BaseFormatter:
    """Factory function to get formatter by type"""
    formatters = {
        'table': TerminalFormatter,
        'json': JSONFormatter, 
        'csv': CSVFormatter
    }
    
    if format_type not in formatters:
        raise ValueError(f"Unsupported format: {format_type}")
    
    return formatters[format_type]()