import pandas as pd
import numpy as np
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any

class TechnicalIndicators:
    """Calculate technical indicators for stock analysis"""
    
    def __init__(self):
        pass
    
    def _validate_data(self, data: pd.DataFrame, required_columns: list) -> None:
        """Validate input data requirements"""
        if data.empty:
            raise ValueError("Input data cannot be empty")
        
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            raise KeyError(f"Missing required columns: {missing_columns}")
    
    def _validate_window(self, window: int, data_length: int, allow_larger: bool = False) -> None:
        """Validate window parameter"""
        if not isinstance(window, int):
            raise TypeError("Window must be an integer")
        if window <= 0:
            raise ValueError("Window must be positive")
        if not allow_larger and window > data_length:
            raise ValueError(f"Window ({window}) cannot be larger than data length ({data_length})")
    
    def _calculate_ema(self, data: pd.Series, window: int) -> pd.Series:
        """Calculate Exponential Moving Average"""
        return data.ewm(span=window, adjust=False).mean()
    
    def calculate_sma(self, data: pd.DataFrame, window: int) -> pd.Series:
        """
        Calculate Simple Moving Average (SMA)
        
        What it measures:
        - The average price of a security over a specific number of periods
        - Smooths out price fluctuations to identify trend direction
        - Used to determine support/resistance levels and trend strength
        
        How it's calculated:
        - Sum of closing prices over 'window' periods divided by window size
        - Formula: SMA = (P1 + P2 + ... + Pn) / n
        - Example: 20-day SMA = sum of last 20 closing prices / 20
        
        Parameters:
        - data: DataFrame with 'Close' column containing stock prices
        - window: Number of periods to average (e.g., 20 for 20-day SMA)
        
        Returns:
        - pd.Series: SMA values, first (window-1) values will be NaN
        
        Trading interpretation:
        - Price above SMA: Bullish signal (uptrend)
        - Price below SMA: Bearish signal (downtrend)
        - Common periods: 20-day (short-term), 50-day (medium), 200-day (long-term)
        - Golden Cross: 50-day SMA crosses above 200-day SMA (bullish)
        - Death Cross: 50-day SMA crosses below 200-day SMA (bearish)
        
        Input requirements:
        - DataFrame must contain 'Close' column
        - window must be positive integer <= len(data)
        """
        # Validate inputs
        self._validate_data(data, ['Close'])
        self._validate_window(window, len(data), allow_larger=True)
        
        # Calculate Simple Moving Average using pandas rolling window
        return data['Close'].rolling(window=window).mean()
    
    def calculate_rsi(self, data: pd.DataFrame, window: int = 14) -> pd.Series:
        """
        Calculate Relative Strength Index (RSI)
        
        What it measures:
        - Momentum oscillator that measures speed and magnitude of price changes
        - Identifies overbought and oversold conditions
        - Ranges from 0 to 100, showing relative strength of recent gains vs losses
        
        How it's calculated:
        - Step 1: Calculate price changes (today's close - yesterday's close)
        - Step 2: Separate gains (positive changes) and losses (negative changes)
        - Step 3: Calculate average gain and average loss over window period
        - Step 4: Calculate Relative Strength (RS) = Average Gain / Average Loss
        - Step 5: RSI = 100 - (100 / (1 + RS))
        - Uses Wilder's smoothing (exponential moving average)
        
        Parameters:
        - data: DataFrame with 'Close' column containing stock prices
        - window: Period for RSI calculation (default 14, standard in trading)
        
        Returns:
        - pd.Series: RSI values between 0-100, first 'window' values will be NaN
        
        Trading interpretation:
        - RSI > 70: Overbought condition (potential sell signal)
        - RSI < 30: Oversold condition (potential buy signal)
        - RSI crossing 50: Momentum shift (above = bullish, below = bearish)
        - Divergence: Price makes new high/low but RSI doesn't (reversal signal)
        - Common periods: 14 (standard), 9 (sensitive), 21 (less sensitive)
        
        Input requirements:
        - DataFrame must contain 'Close' column
        - window must be positive integer >= 2
        - Need at least window+1 data points for calculation
        """
        # Validate inputs
        self._validate_data(data, ['Close'])
        self._validate_window(window, len(data))
        
        # Calculate price changes
        price_changes = data['Close'].diff()
        
        # Separate gains and losses
        gains = price_changes.where(price_changes > 0, 0)
        losses = -price_changes.where(price_changes < 0, 0)
        
        # Calculate average gains and losses using Wilder's smoothing (EMA with alpha = 1/window)
        # For traditional RSI, we need at least 'window' periods of gains/losses to start calculation
        # Since price_changes starts with NaN (first diff), we effectively need window+1 data points
        avg_gains = gains.ewm(alpha=1/window, adjust=False).mean()
        avg_losses = losses.ewm(alpha=1/window, adjust=False).mean()
        
        # Calculate RS and RSI 
        rs = avg_gains / avg_losses
        rsi = 100 - (100 / (1 + rs))
        
        # Set first 'window' RSI values to NaN to match traditional RSI calculation
        rsi.iloc[:window] = np.nan
        
        return rsi
    
    def calculate_macd(self, data: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        Calculate MACD (Moving Average Convergence Divergence)
        
        What it measures:
        - Trend-following momentum indicator showing relationship between two EMAs
        - Reveals changes in strength, direction, momentum of a stock's price trend
        - Consists of MACD line, Signal line, and Histogram
        
        How it's calculated:
        - Step 1: Calculate fast EMA (typically 12-period)
        - Step 2: Calculate slow EMA (typically 26-period)  
        - Step 3: MACD Line = Fast EMA - Slow EMA
        - Step 4: Signal Line = EMA of MACD Line (typically 9-period)
        - Step 5: Histogram = MACD Line - Signal Line
        - EMA = Exponential Moving Average (more weight to recent prices)
        
        Parameters:
        - data: DataFrame with 'Close' column containing stock prices
        - fast: Period for fast EMA (default 12, standard setting)
        - slow: Period for slow EMA (default 26, standard setting)
        - signal: Period for signal line EMA (default 9, standard setting)
        
        Returns:
        - Tuple of three pd.Series:
          1. MACD Line: Difference between fast and slow EMAs
          2. Signal Line: EMA of MACD line
          3. Histogram: MACD line minus signal line
        
        Trading interpretation:
        - MACD above Signal: Bullish momentum (buy signal)
        - MACD below Signal: Bearish momentum (sell signal)
        - MACD crosses above Signal: Golden cross (strong buy)
        - MACD crosses below Signal: Death cross (strong sell)
        - Histogram: Shows momentum strength (larger bars = stronger momentum)
        - Zero line crosses: MACD crossing zero indicates trend changes
        - Divergence: Price vs MACD divergence signals potential reversals
        
        Input requirements:
        - DataFrame must contain 'Close' column
        - fast < slow (fast period must be less than slow period)
        - All parameters must be positive integers
        - Need at least slow+signal data points for full calculation
        """
        # Validate inputs
        self._validate_data(data, ['Close'])
        if fast >= slow:
            raise ValueError("Fast period must be less than slow period")
        
        # Calculate EMAs
        fast_ema = self._calculate_ema(data['Close'], fast)
        slow_ema = self._calculate_ema(data['Close'], slow)
        
        # Calculate MACD line
        macd_line = fast_ema - slow_ema
        
        # Calculate Signal line (EMA of MACD line)
        signal_line = self._calculate_ema(macd_line, signal)
        
        # Calculate Histogram
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    def generate_macd_signals(self, data: pd.DataFrame) -> pd.Series:
        """
        Generate buy/sell signals from MACD crossovers
        
        What it measures:
        - Discrete trading signals based on MACD line and Signal line crossovers
        - Converts continuous MACD values into actionable buy/sell/hold signals
        - Helps automate trading decisions based on momentum changes
        
        How it's calculated:
        - Step 1: Calculate MACD line and Signal line using calculate_macd()
        - Step 2: Identify crossover points:
          * When MACD crosses above Signal: Generate BUY signal (+1)
          * When MACD crosses below Signal: Generate SELL signal (-1)
          * Otherwise: Hold signal (0)
        - Step 3: Use shift() to detect crossovers between consecutive periods
        
        Parameters:
        - data: DataFrame with 'Close' column containing stock prices
        
        Returns:
        - pd.Series: Signal values (-1, 0, +1) where:
          * +1 = Buy signal (MACD crossed above Signal line)
          * -1 = Sell signal (MACD crossed below Signal line)  
          * 0 = Hold/No signal (no crossover occurred)
        
        Trading interpretation:
        - +1 (Buy): Enter long position, momentum turning bullish
        - -1 (Sell): Exit long/enter short, momentum turning bearish
        - 0 (Hold): Maintain current position, no momentum change
        - Best used with trend confirmation from other indicators
        - Works better in trending markets vs sideways markets
        
        Input requirements:
        - DataFrame must contain 'Close' column
        - Same requirements as calculate_macd() method
        - Signals will have NaN values until MACD calculation is valid
        """
        # Calculate MACD components
        macd_line, signal_line, _ = self.calculate_macd(data)
        
        # Initialize signals series with zeros
        signals = pd.Series(0, index=data.index)
        
        # Generate crossover signals
        # Buy signal (+1) when MACD crosses above Signal
        # Sell signal (-1) when MACD crosses below Signal
        prev_macd = macd_line.shift(1)
        prev_signal = signal_line.shift(1)
        
        # Crossover conditions
        bullish_crossover = (macd_line > signal_line) & (prev_macd <= prev_signal)
        bearish_crossover = (macd_line < signal_line) & (prev_macd >= prev_signal)
        
        signals.loc[bullish_crossover] = 1   # Buy signal
        signals.loc[bearish_crossover] = -1  # Sell signal
        
        return signals
    
    def calculate_bollinger_bands(self, data: pd.DataFrame, window: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        Calculate Bollinger Bands
        
        What it measures:
        - Volatility indicator consisting of moving average with upper/lower bands
        - Shows relative high and low prices compared to recent trading range
        - Bands expand during high volatility, contract during low volatility
        - Helps identify overbought/oversold conditions and potential breakouts
        
        How it's calculated:
        - Step 1: Calculate Simple Moving Average (typically 20-period)
        - Step 2: Calculate standard deviation of closing prices over same period
        - Step 3: Upper Band = SMA + (std_dev × Standard Deviation)
        - Step 4: Lower Band = SMA - (std_dev × Standard Deviation)
        - Step 5: Middle Band = SMA (same as calculate_sma result)
        - Standard settings: 20-period SMA, 2 standard deviations
        
        Parameters:
        - data: DataFrame with 'Close' column containing stock prices
        - window: Period for SMA and standard deviation (default 20, standard)
        - std_dev: Number of standard deviations for bands (default 2.0, standard)
        
        Returns:
        - Tuple of three pd.Series:
          1. Upper Band: SMA + (std_dev × standard deviation)
          2. Middle Band: Simple Moving Average (same as SMA)
          3. Lower Band: SMA - (std_dev × standard deviation)
        
        Trading interpretation:
        - Price near Upper Band: Overbought, potential sell signal
        - Price near Lower Band: Oversold, potential buy signal
        - Price touching bands ~95% of time (with 2 std dev setting)
        - Band squeeze: Low volatility, potential breakout coming
        - Band expansion: High volatility, strong trending move
        - Bollinger Bounce: Price bounces off bands back toward middle
        - Bollinger Breakout: Price breaks through bands, continuation signal
        - %B indicator: (Price - Lower Band) / (Upper Band - Lower Band)
        
        Input requirements:
        - DataFrame must contain 'Close' column
        - window must be positive integer >= 2
        - std_dev must be positive float
        - Need at least 'window' data points for calculation
        """
        # Validate inputs
        self._validate_data(data, ['Close'])
        self._validate_window(window, len(data))
        if std_dev <= 0:
            raise ValueError("Standard deviation multiplier must be positive")
        
        # Calculate middle band (Simple Moving Average)
        middle_band = self.calculate_sma(data, window)
        
        # Calculate standard deviation
        rolling_std = data['Close'].rolling(window=window).std()
        
        # Calculate upper and lower bands
        upper_band = middle_band + (std_dev * rolling_std)
        lower_band = middle_band - (std_dev * rolling_std)
        
        return upper_band, middle_band, lower_band
    
    def calculate_volume_sma(self, data: pd.DataFrame, window: int = 20) -> pd.Series:
        """
        Calculate Volume Simple Moving Average
        
        What it measures:
        - Average trading volume over a specified period
        - Shows typical volume levels to identify unusual activity
        - Helps confirm price movements and detect accumulation/distribution
        - Volume precedes price - unusual volume often signals price moves
        
        How it's calculated:
        - Sum of volume over 'window' periods divided by window size
        - Formula: Volume SMA = (V1 + V2 + ... + Vn) / n
        - Same calculation as price SMA but applied to volume data
        - Example: 20-day Volume SMA = sum of last 20 volume values / 20
        
        Parameters:
        - data: DataFrame with 'Volume' column containing trading volumes
        - window: Number of periods to average (default 20, common setting)
        
        Returns:
        - pd.Series: Volume SMA values, first (window-1) values will be NaN
        
        Trading interpretation:
        - Volume above SMA: Higher than normal activity (significant)
        - Volume below SMA: Lower than normal activity (less significant)
        - High volume + price breakout: Strong confirmation of move
        - Low volume + price move: Weak move, likely to reverse
        - Volume spikes: Often precede major price movements
        - Used with relative volume ratio for better context
        
        Input requirements:
        - DataFrame must contain 'Volume' column
        - window must be positive integer <= len(data)
        - Volume values should be non-negative
        """
        # Validate inputs
        self._validate_data(data, ['Volume'])
        self._validate_window(window, len(data), allow_larger=True)
        
        # Calculate Volume Simple Moving Average
        return data['Volume'].rolling(window=window).mean()
    
    def calculate_relative_volume(self, data: pd.DataFrame, window: int = 20) -> pd.Series:
        """
        Calculate Relative Volume (RVOL)
        
        What it measures:
        - Current volume compared to average volume over recent periods
        - Shows how unusual today's trading activity is compared to normal
        - Values above 1.0 indicate higher than average volume
        - Helps identify significant market interest and potential price moves
        
        How it's calculated:
        - Step 1: Calculate Volume SMA over 'window' periods
        - Step 2: Divide current volume by Volume SMA
        - Formula: RVOL = Current Volume / Volume SMA
        - Example: If today's volume is 2M and 20-day avg is 1M, RVOL = 2.0
        
        Parameters:
        - data: DataFrame with 'Volume' column containing trading volumes
        - window: Period for volume average calculation (default 20)
        
        Returns:
        - pd.Series: Relative volume ratios where:
          * 1.0 = Average volume (normal activity)
          * > 1.0 = Above average volume (unusual activity)
          * < 1.0 = Below average volume (quiet trading)
          * 2.0 = Double the normal volume
          * 0.5 = Half the normal volume
        
        Trading interpretation:
        - RVOL > 2.0: Very high volume, significant event likely
        - RVOL 1.5-2.0: Above average, increased interest
        - RVOL 0.8-1.2: Normal volume, typical trading
        - RVOL < 0.8: Below average, low interest
        - High RVOL + price breakout: Strong confirmation
        - High RVOL + price reversal: Potential exhaustion/climax
        - Use with price action for context
        
        Input requirements:
        - DataFrame must contain 'Volume' column
        - window must be positive integer
        - Same requirements as calculate_volume_sma()
        - First 'window' values will be NaN
        """
        # Calculate volume SMA first
        volume_sma = self.calculate_volume_sma(data, window)
        
        # Calculate relative volume (current volume / volume SMA)
        relative_volume = data['Volume'] / volume_sma
        
        return relative_volume
    
    def _get_latest_indicator_value(self, series: pd.Series, min_len: int) -> Optional[float]:
        """Helper to get the latest value from a series if data length is sufficient."""
        if len(series) >= min_len:
            latest_val = series.iloc[-1]
            return float(latest_val) if not pd.isna(latest_val) else None
        return None

    def _get_latest_macd_values(self, data: pd.DataFrame) -> Tuple[Optional[float], Optional[float], Optional[float], Optional[int]]:
        """Helper to calculate and return latest MACD values and crossover signal."""
        if len(data) >= 35: # Need at least slow + signal periods
            macd_line, signal_line, histogram = self.calculate_macd(data)
            macd_line_val = self._get_latest_indicator_value(macd_line, 0)
            macd_signal_val = self._get_latest_indicator_value(signal_line, 0)
            macd_histogram_val = self._get_latest_indicator_value(histogram, 0)
            
            macd_signals = self.generate_macd_signals(data)
            macd_crossover = int(macd_signals.iloc[-1]) if not pd.isna(macd_signals.iloc[-1]) else 0
            return macd_line_val, macd_signal_val, macd_histogram_val, macd_crossover
        return None, None, None, None


    def get_indicator_summary(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Get comprehensive summary of all key technical indicators
        
        What it provides:
        - One-stop summary of all calculated technical indicators
        - Latest values for trend, momentum, and volume indicators
        - Current trading signals and market conditions
        - Easy-to-use overview for decision making
        
        How it works:
        - Step 1: Calculate all individual indicators using their respective methods
        - Step 2: Extract the most recent (latest) value from each indicator
        - Step 3: Generate trading signals where applicable (MACD crossovers)
        - Step 4: Determine overall market condition based on multiple indicators
        - Step 5: Return structured dictionary with all key metrics
        
        Parameters:
        - data: DataFrame with 'Close' and 'Volume' columns containing stock data
        
        Returns:
        - Dict[str, Any]: Comprehensive indicator summary containing:
          * 'current_price': Latest closing price
          * 'sma_20': 20-day Simple Moving Average
          * 'sma_50': 50-day Simple Moving Average
          * 'sma_200': 200-day Simple Moving Average (long-term trend)
          * 'rsi': Current RSI value (overbought/oversold)
          * 'macd_line': Current MACD line value
          * 'macd_signal': Current MACD signal line value
          * 'macd_histogram': Current MACD histogram value
          * 'macd_crossover': Latest MACD signal (+1/0/-1)
          * 'bb_upper': Upper Bollinger Band value
          * 'bb_middle': Middle Bollinger Band (20-day SMA)
          * 'bb_lower': Lower Bollinger Band value
          * 'volume_sma': 20-day Volume Simple Moving Average
          * 'relative_volume': Current relative volume ratio
          * 'trend_signal': Overall trend assessment ('bullish'/'bearish'/'neutral')
          * 'momentum_signal': Momentum assessment based on RSI and MACD
        
        Trading interpretation:
        - Use as dashboard for quick market assessment
        - 'trend_signal': Primary trend direction based on multiple SMAs
        - 'momentum_signal': Short-term momentum based on RSI/MACD
        - Cross-reference multiple indicators for confirmation
        - High relative_volume adds conviction to signals
        - RSI extremes (>70, <30) suggest caution on trend signals
        
        Input requirements:
        - DataFrame must contain 'Close' and 'Volume' columns
        - Need sufficient data for all indicator calculations (recommend 200+ periods)
        - Will return partial data if some indicators cannot be calculated
        """
        # Validate inputs
        self._validate_data(data, ['Close', 'Volume'])
        
        # Calculate all indicators with individual error handling
        current_price = self._get_latest_indicator_value(data['Close'], 1) if not data.empty else None
        
        # Calculate SMAs with individual error handling
        sma_20 = None
        sma_50 = None
        sma_200 = None
        try:
            sma_20 = self._get_latest_indicator_value(self.calculate_sma(data, 20), 20)
        except (ValueError, Exception):
            pass
        try:
            sma_50 = self._get_latest_indicator_value(self.calculate_sma(data, 50), 50)
        except (ValueError, Exception):
            pass
        try:
            sma_200 = self._get_latest_indicator_value(self.calculate_sma(data, 200), 200)
        except (ValueError, Exception):
            pass
        
        # Calculate RSI with error handling
        rsi = None
        try:
            rsi = self._get_latest_indicator_value(self.calculate_rsi(data, 14), 15)
        except (ValueError, Exception):
            pass
        
        # MACD components with error handling
        macd_line_val, macd_signal_val, macd_histogram_val, macd_crossover = None, None, None, None
        try:
            macd_line_val, macd_signal_val, macd_histogram_val, macd_crossover = self._get_latest_macd_values(data)
        except (ValueError, Exception):
            pass
        
        # Bollinger Bands with error handling
        bb_upper_val, bb_middle_val, bb_lower_val = (None, None, None)
        try:
            if len(data) >= 20: # BB requires at least 20 periods
                bb_upper, bb_middle, bb_lower = self.calculate_bollinger_bands(data)
                bb_upper_val = self._get_latest_indicator_value(bb_upper, 0)
                bb_middle_val = self._get_latest_indicator_value(bb_middle, 0)
                bb_lower_val = self._get_latest_indicator_value(bb_lower, 0)
        except (ValueError, Exception):
            pass
        
        # New Bollinger Band indicators (Micro-Phase 1C)
        bollinger_percent_b = None
        bollinger_squeeze = {'is_squeeze': False, 'squeeze_intensity': None, 'squeeze_periods': 0}
        bollinger_breakout = {'upper_breakout': False, 'lower_breakout': False, 'breakout_strength': None}
        bb_status = 'insufficient_data'  # Track why BB analysis unavailable
        
        if len(data) >= 20:  # BB-derived indicators require sufficient data
            try:
                percent_b_series = self.calculate_bollinger_percent_b(data)
                bollinger_percent_b = self._get_latest_indicator_value(percent_b_series, 0)
                
                bollinger_squeeze = self.detect_bollinger_squeeze(data)
                bollinger_breakout = self.detect_bollinger_breakouts(data)
                bb_status = 'available'
                
            except Exception as e:
                # Log error and provide fallback values
                bb_status = f'calculation_error: {str(e)}'
                # Keep default fallback values set above
        else:
            bb_status = f'insufficient_data: {len(data)}/20 periods'
        
        # Volume indicators with error handling
        volume_sma = None
        relative_volume = None
        try:
            volume_sma = self._get_latest_indicator_value(self.calculate_volume_sma(data, 20), 20)
        except (ValueError, Exception):
            pass
        try:
            relative_volume = self._get_latest_indicator_value(self.calculate_relative_volume(data, 20), 20)
        except (ValueError, Exception):
            pass
        
        # Generate overall signals
        trend_signal = self._determine_trend_signal(current_price, sma_20, sma_50, sma_200)
        momentum_signal = self._determine_momentum_signal(rsi, macd_crossover)
        
        return { # Return all calculated indicators
            'current_price': current_price, 'sma_20': sma_20, 'sma_50': sma_50, 'sma_200': sma_200,
            'rsi': rsi, 'macd_line': macd_line_val, 'macd_signal': macd_signal_val,
            'macd_histogram': macd_histogram_val, 'macd_crossover': macd_crossover,
            'bb_upper': bb_upper_val, 'bb_middle': bb_middle_val, 'bb_lower': bb_lower_val,
            'bollinger_percent_b': bollinger_percent_b, 'bollinger_squeeze': bollinger_squeeze,
            'bollinger_breakout': bollinger_breakout, 'bb_status': bb_status,
            'volume_sma': volume_sma, 'relative_volume': relative_volume,
            'trend_signal': trend_signal, 'momentum_signal': momentum_signal
        }
    
    def _determine_trend_signal(self, current_price, sma_20, sma_50, sma_200):
        """Determine overall trend based on multiple SMAs"""
        if None in [current_price, sma_20]:
            return 'neutral'
        
        bullish_signals = 0
        bearish_signals = 0
        
        # Price vs SMAs
        if current_price > sma_20:
            bullish_signals += 1
        else:
            bearish_signals += 1
            
        if sma_50 is not None:
            if current_price > sma_50:
                bullish_signals += 1
            else:
                bearish_signals += 1
                
        if sma_200 is not None:
            if current_price > sma_200:
                bullish_signals += 1
            else:
                bearish_signals += 1
        
        if bullish_signals > bearish_signals:
            return 'bullish'
        elif bearish_signals > bullish_signals:
            return 'bearish'
        else:
            return 'neutral'
    
    def _determine_momentum_signal(self, rsi, macd_crossover):
        """Determine momentum based on RSI and MACD"""
        if rsi is None and macd_crossover is None:
            return 'neutral'
        
        signals = []
        
        if rsi is not None:
            if rsi > 70:
                signals.append('bearish')  # Overbought
            elif rsi < 30:
                signals.append('bullish')  # Oversold
            else:
                signals.append('neutral')
        
        if macd_crossover is not None:
            if macd_crossover > 0:
                signals.append('bullish')
            elif macd_crossover < 0:
                signals.append('bearish')
            else:
                signals.append('neutral')
        
        # Aggregate signals
        bullish_count = signals.count('bullish')
        bearish_count = signals.count('bearish')
        
        if bullish_count > bearish_count:
            return 'bullish'
        elif bearish_count > bullish_count:
            return 'bearish'
        else:
            return 'neutral'
    
    def detect_support_resistance(self, data: pd.DataFrame, window: int = 20, include_strength: bool = False) -> Dict[str, Any]:
        """
        Detect support and resistance levels using local minima/maxima analysis
        
        Support levels are price levels where the stock tends to find buying interest,
        preventing further decline. Resistance levels are price levels where selling
        pressure emerges, preventing further price appreciation.
        
        Parameters:
        - data: DataFrame with OHLC data
        - window: Lookback period for local extrema detection
        - include_strength: Whether to calculate strength scores for levels
        
        Returns:
        - Dictionary containing support_levels, resistance_levels, and optionally strength scores
        """
        # Input validation
        if data.empty:
            raise ValueError("Input data cannot be empty")
        
        required_columns = ['Close', 'High', 'Low']
        self._validate_data(data, required_columns)
        
        # Handle insufficient data
        if len(data) < window:
            result = {
                'support_levels': [],
                'resistance_levels': []
            }
            if include_strength:
                result.update({
                    'support_strength': [],
                    'resistance_strength': []
                })
            return result
        
        # Extract price series
        highs = data['High'].values
        lows = data['Low'].values
        
        # Find local minima (support levels)
        support_indices = []
        for i in range(window, len(lows) - window):
            if lows[i] == min(lows[i-window:i+window+1]):
                support_indices.append(i)
        
        # Find local maxima (resistance levels) 
        resistance_indices = []
        for i in range(window, len(highs) - window):
            if highs[i] == max(highs[i-window:i+window+1]):
                resistance_indices.append(i)
        
        # Extract price levels
        support_levels = [lows[i] for i in support_indices]
        resistance_levels = [highs[i] for i in resistance_indices]
        
        # Filter out levels that are too close to each other (within 2%)
        support_levels = self._filter_nearby_levels(support_levels)
        resistance_levels = self._filter_nearby_levels(resistance_levels)
        
        # Build result dictionary
        result = {
            'support_levels': support_levels,
            'resistance_levels': resistance_levels
        }
        
        # Calculate strength scores if requested
        if include_strength:
            support_strength = [self._calculate_level_strength(level, lows, 'support') for level in support_levels]
            resistance_strength = [self._calculate_level_strength(level, highs, 'resistance') for level in resistance_levels]
            
            result.update({
                'support_strength': support_strength,
                'resistance_strength': resistance_strength
            })
        
        return result
    
    def _filter_nearby_levels(self, levels: list, threshold: float = 0.02) -> list:
        """Filter out price levels that are too close to each other"""
        if not levels:
            return []
        
        levels = sorted(levels)
        filtered = [levels[0]]
        
        for level in levels[1:]:
            if abs(level - filtered[-1]) / filtered[-1] > threshold:
                filtered.append(level)
        
        return filtered
    
    def _calculate_level_strength(self, level: float, prices: np.ndarray, level_type: str) -> float:
        """Calculate strength score for a support/resistance level"""
        touches = 0
        tolerance = 0.01  # 1% tolerance for level touches
        
        for price in prices:
            if abs(price - level) / level <= tolerance:
                touches += 1
        
        # Normalize strength score between 0 and 1
        # More touches = higher strength, cap at reasonable maximum
        max_touches = 5
        strength = min(touches / max_touches, 1.0)
        
        return float(strength)
    
    def calculate_bollinger_percent_b(self, data: pd.DataFrame, window: int = 20, num_std: float = 2.0) -> pd.Series:
        """
        Calculate Bollinger Band %B indicator - price position within the bands
        
        %B = (Price - Lower Band) / (Upper Band - Lower Band)
        
        Values interpretation:
        - %B = 1.0: Price at upper band (potential overbought)
        - %B = 0.5: Price at middle band (20-day SMA)
        - %B = 0.0: Price at lower band (potential oversold)
        - %B > 1.0: Price above upper band (strong breakout)
        - %B < 0.0: Price below lower band (strong breakdown)
        
        Parameters:
        - data: DataFrame with 'Close' column
        - window: Period for moving average calculation (default 20)
        - num_std: Number of standard deviations for bands (default 2.0)
        
        Returns:
        - pd.Series: %B values for each period
        """
        if data.empty:
            raise ValueError("Input data cannot be empty")
        
        if 'Close' not in data.columns:
            raise ValueError("Data must contain 'Close' column")
        
        # Calculate Bollinger Bands - returns (upper, middle, lower)
        try:
            upper_band, middle_band, lower_band = self.calculate_bollinger_bands(data, window, num_std)
        except ValueError as e:
            # Handle case where window is larger than data
            if "Window" in str(e) and "larger than data length" in str(e):
                # Return NaN series for insufficient data
                return pd.Series([np.nan] * len(data), index=data.index, name='bollinger_percent_b')
            else:
                raise e
        
        # Calculate %B = (Price - Lower Band) / (Upper Band - Lower Band)
        close_prices = data['Close']
        
        # Avoid division by zero when bands are too close
        band_width = upper_band - lower_band
        percent_b = np.where(
            band_width > 1e-10,  # Small threshold to avoid division by zero
            (close_prices - lower_band) / band_width,
            np.nan
        )
        
        return pd.Series(percent_b, index=data.index, name='bollinger_percent_b')
    
    def detect_bollinger_squeeze(self, data: pd.DataFrame, window: int = 20, 
                                squeeze_threshold: float = 0.1, lookback_periods: int = 50) -> Dict[str, Any]:
        """
        Detect Bollinger Band squeeze conditions (low volatility periods)
        
        Bollinger squeeze occurs when the bands contract, indicating:
        - Low volatility period
        - Potential breakout opportunity
        - Market consolidation
        
        Parameters:
        - data: DataFrame with 'Close' column
        - window: Period for Bollinger Bands calculation (default 20)
        - squeeze_threshold: Threshold for squeeze detection (default 0.1)
        - lookback_periods: Periods to analyze for squeeze history (default 50)
        
        Returns:
        - Dict containing:
          * 'is_squeeze': bool - Currently in squeeze
          * 'squeeze_intensity': float - Intensity of squeeze (0-1)
          * 'squeeze_periods': int - Consecutive periods in squeeze
        """
        if data.empty:
            raise ValueError("Input data cannot be empty")
        
        if 'Close' not in data.columns:
            raise ValueError("Data must contain 'Close' column")
        
        # Calculate Bollinger Bands - returns (upper, middle, lower)
        try:
            upper_band, middle_band, lower_band = self.calculate_bollinger_bands(data, window)
        except ValueError:
            # Handle insufficient data gracefully
            return {
                'is_squeeze': False,
                'squeeze_intensity': np.nan,
                'squeeze_periods': 0
            }
        
        # Calculate band width as percentage of middle band (SMA)
        
        # Band width ratio = (Upper - Lower) / Middle
        band_width_ratio = (upper_band - lower_band) / middle_band
        
        # Get recent data for analysis
        recent_data = band_width_ratio.tail(lookback_periods).dropna()
        
        if len(recent_data) < 10:  # Need minimum data for analysis
            return {
                'is_squeeze': False,
                'squeeze_intensity': np.nan,
                'squeeze_periods': 0
            }
        
        # Current band width
        current_width = recent_data.iloc[-1] if not recent_data.empty else np.nan
        
        if pd.isna(current_width):
            return {
                'is_squeeze': False,
                'squeeze_intensity': np.nan,
                'squeeze_periods': 0
            }
        
        # Historical percentile for squeeze detection
        historical_percentile = recent_data.quantile(0.20)  # 20th percentile
        
        # Squeeze conditions
        is_squeeze = current_width <= historical_percentile and current_width < squeeze_threshold
        
        # Calculate squeeze intensity (inverse of width ratio)
        max_width = recent_data.max()
        min_width = recent_data.min()
        
        if max_width > min_width:
            # Normalize intensity: lower width = higher intensity
            intensity = 1.0 - ((current_width - min_width) / (max_width - min_width))
        else:
            intensity = 0.5
        
        # Count consecutive squeeze periods
        squeeze_periods = 0
        for i in range(len(recent_data) - 1, -1, -1):
            width = recent_data.iloc[i]
            if width <= historical_percentile and width < squeeze_threshold:
                squeeze_periods += 1
            else:
                break
        
        return {
            'is_squeeze': bool(is_squeeze),
            'squeeze_intensity': float(intensity),
            'squeeze_periods': int(squeeze_periods)
        }
    
    def detect_bollinger_breakouts(self, data: pd.DataFrame, window: int = 20,
                                  volume_confirmation: bool = True) -> Dict[str, Any]:
        """
        Detect Bollinger Band breakout signals
        
        Breakout occurs when price moves beyond the bands, indicating:
        - Strong directional momentum
        - Potential continuation of trend
        - High probability trading setup (with volume confirmation)
        
        Parameters:
        - data: DataFrame with 'Close' and optionally 'Volume' columns
        - window: Period for Bollinger Bands calculation (default 20)
        - volume_confirmation: Whether to check volume for confirmation
        
        Returns:
        - Dict containing:
          * 'upper_breakout': bool - Price broke above upper band
          * 'lower_breakout': bool - Price broke below lower band  
          * 'breakout_strength': float - Strength of breakout (0-1)
        """
        if data.empty:
            raise ValueError("Input data cannot be empty")
        
        if 'Close' not in data.columns:
            raise ValueError("Data must contain 'Close' column")
        
        # Calculate %B (which handles Bollinger Bands internally)
        percent_b = self.calculate_bollinger_percent_b(data, window)
        
        if len(data) < 2:
            return {
                'upper_breakout': False,
                'lower_breakout': False,
                'breakout_strength': np.nan
            }
        
        # Current and previous %B values
        current_percent_b = percent_b.iloc[-1] if not percent_b.empty else np.nan
        previous_percent_b = percent_b.iloc[-2] if len(percent_b) >= 2 else np.nan
        
        if pd.isna(current_percent_b):
            return {
                'upper_breakout': False,
                'lower_breakout': False,
                'breakout_strength': np.nan
            }
        
        # Breakout detection
        upper_breakout = current_percent_b > 1.0  # Above upper band
        lower_breakout = current_percent_b < 0.0  # Below lower band
        
        # Calculate breakout strength
        if upper_breakout:
            # Strength based on how far above upper band
            strength = min((current_percent_b - 1.0) * 2, 1.0)  # Scale and cap at 1.0
        elif lower_breakout:
            # Strength based on how far below lower band
            strength = min(abs(current_percent_b) * 2, 1.0)  # Scale and cap at 1.0
        else:
            strength = 0.0
        
        # Volume confirmation (if available)
        if volume_confirmation and 'Volume' in data.columns:
            try:
                volume_analysis = self.analyze_volume(data)
                relative_volume = volume_analysis.get('relative_volume', 1.0)
                
                # Boost strength with volume confirmation
                if relative_volume > 1.5:  # High volume
                    strength = min(strength * 1.3, 1.0)  # 30% boost, capped at 1.0
                elif relative_volume < 0.8:  # Low volume
                    strength = strength * 0.7  # Reduce strength by 30%
                    
            except Exception:
                # If volume analysis fails, proceed without volume confirmation
                pass
        
        return {
            'upper_breakout': bool(upper_breakout),
            'lower_breakout': bool(lower_breakout),
            'breakout_strength': float(strength) if not pd.isna(strength) else np.nan
        }