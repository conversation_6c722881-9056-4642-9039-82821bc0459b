#!/bin/bash

# Check if a file path is provided
if [ -z "$1" ]; then
  echo "Usage: $0 <path_to_csv_file>"
  exit 1
fi

CSV_FILE="$1"

# Check if the file exists
if [ ! -f "$CSV_FILE" ]; then
  echo "Error: File not found at $CSV_FILE"
  exit 1
fi

# Read the CSV file, skip the header, and run the analysis for each stock symbol
tail -n +2 "$CSV_FILE" | while IFS=, read -r symbol _;
  # Trim leading/trailing whitespace from the symbol
  do
  trimmed_symbol=$(echo "$symbol" | xargs)
  echo "Analyzing $trimmed_symbol..."
  python main.py analyze "$trimmed_symbol.BO"
  echo "------------------------"
done

echo "Analysis complete."
